//
//  ServiceDetailView.swift
//  Appio
//
//  Created by gondo on 06/03/2025.
//

import SwiftUI

struct ServiceDetailView: View {
    let service: ServiceEntity
    let device: DeviceEntity?

    @Environment(\.dismiss) var dismiss
    
    var body: some View {
        ZStack {
            VStack(spacing: 0) {
                AsyncImageView(urlString: service.bannerURL)
                    .aspectRatio(16 / 9, contentMode: .fit)
                    .clipped()
                    .accessibilityLabel(Text("\(service.title) Banner Image"))
                    .clipShape(ContainerRelativeShape())
                    .overlay(
                        ContainerRelativeShape()
                            .strokeBorder(.ultraThinMaterial, lineWidth: 1.5)
                    )

                VStack {
                    HStack(alignment: .top, spacing: 24) {
                        AsyncImageView(
                            urlString: service.logoURL,
                            cornerRadius: UIConstants.logoCornerRadius
                        )
                        .frame(width: UIConstants.logoSize, height: UIConstants.logoSize)
                        .accessibilityLabel(Text("\(service.title) Logo"))

                        VStack(alignment: .leading) {
                            Text(service.title)
                                .font(.title2)
                                .fontWeight(.semibold)
                                .accessibilityAddTraits(.isHeader)
                            Text(service.description ?? "")
                                .font(.callout)
                                .foregroundStyle(.secondary)
                        }

                        Spacer()
                    }
                    
                    if let link = service.URL,
                       let url = URL(string: link),
                       UIApplication.shared.canOpenURL(url)
                    {
                        AppioButton("Open Website") {
                            UIApplication.shared.open(url)
                        }
                        .padding(UIConstants.largeSpacing)
                    }
                    
                    Spacer()
                    
                    AppioFooterView(service: service, device: device)
                        .padding(.bottom, 32)
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)

                Spacer()
            }
        }
        .ignoresSafeArea(.all)
    }
}

#Preview {
    #if DEBUG
    ServiceDetailView(service: ServiceEntity.mock, device: DeviceEntity.mock)
    #endif
}
